import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Box } from "@mui/material";
import ViewIcon from "@/assets/Icons/Viewicon.svg?react";
import EditorIcon from "@/assets/Icons/EditorIcon.svg?react";
import UserSettingIcon from "@/assets/Icons/UserSetting.svg?react";

import UnionIcon from "@/assets/Icons/UnionIcon.svg?react";
import DeleteIcon from "@/assets/Icons/DeteleIcon.svg?react";
import SendEmail from "@/assets/Icons/sendEmail.svg?react";
import AuthButton from "../AuthButton";
import { useTranslation } from "react-i18next";

const iconStyle = { fontSize: 12, width: 20, height: 20 };
const actionBtnStyle = { minWidth: "40px" };

const ZKTableRowActions = ({
  row,
  renderRowActions,
  isShowAction = {},
  handlers = {},
  customActionPosition = "before-delete", // 新增参数控制位置
}) => {
  const isDefault = row.original.isDefault == "1";
  const isAdmin = row.original.isAdmin == "1" || row.original.isAdmin == null;
  const { t } = useTranslation();
  // 获取自定义操作配置
  const customActionConfig = renderRowActions
    ? renderRowActions({ row })
    : null;

  const defaultActions = [
    {
      key: "view",
      show: isDefault ? false : isShowAction.isShowView,
      cb: handlers.handlerView,
      customShow: true,
      icon: <ViewIcon style={{ width: 26, height: 26 }} />,
      title: "common.common_view",
    },
    {
      key: "editor",
      show: isDefault ? false : isShowAction.isShowEditor,
      cb: handlers.handlerEditor,
      customShow: true,
      icon: <EditorIcon style={iconStyle} />,
      title: "common.common_editor",
    },
    {
      key: "userSetting",
      show: isDefault ? false : isShowAction.isShowUserSetting,
      cb: handlers.handlerUserSetting,
      customShow: true,
      icon: <UserSettingIcon style={iconStyle} />,
      title: "common.common_user_setting",
    },
    {
      key: "union",
      show: isShowAction.isShowUnion,
      cb: handlers.handlerUnion,
      customShow: true,
      icon: <UnionIcon style={{ width: 18, height: 18 }} />,
      title: "common.common_union",
    },
    {
      key: "delete",
      show: isDefault ? false : isShowAction.isShowDetele,
      customShow: isAdmin,
      cb: handlers.Detele,
      icon: <DeleteIcon style={iconStyle} />,
      title: "common.common_delete",
    },
    {
      key: "sendEmail",
      show: isShowAction.isShowSendEamil,
      cb: handlers.handlerSendEmail,
      customShow: true,
      icon: <SendEmail style={iconStyle} />,
      title: "common.common_send_email",
    },
  ];

  // 根据位置插入自定义操作
  const insertCustomAction = (actions, customAction, position) => {
    if (!customAction) return actions;

    const customActionItem = {
      key: "custom",
      show: true,
      custom: true,
      element: customAction,
    };

    switch (position) {
      case "before-delete":
        const deleteIndex = actions.findIndex(
          (action) => action.key === "delete"
        );
        if (deleteIndex !== -1) {
          actions.splice(deleteIndex, 0, customActionItem);
        } else {
          actions.push(customActionItem);
        }
        break;
      case "first":
        actions.unshift(customActionItem);
        break;
      case "last":
        actions.push(customActionItem);
        break;
      default:
        actions.push(customActionItem);
    }

    return actions;
  };

  const finalActions = insertCustomAction(
    [...defaultActions],
    customActionConfig,
    customActionPosition
  );

  return (
    <Grid sx={{ display: "flex", justifyContent: "center" }}>
      {finalActions.map((btn, i) => {
        // 如果是自定义操作，直接渲染
        if (btn.custom) {
          return <Box key={btn.key}>{btn.element}</Box>;
        }

        if (!btn.show) return null;

        if (!btn.customShow) return null;

        // 渲染默认操作
        return (
          <AuthButton key={btn.key} button={btn.show}>
            <Tooltip title={t(btn.title)}>
              <Button sx={actionBtnStyle} onClick={() => btn.cb(row.original)}>
                {btn.icon}
              </Button>
            </Tooltip>
          </AuthButton>
        );
      })}
    </Grid>
  );
};

export default ZKTableRowActions;
