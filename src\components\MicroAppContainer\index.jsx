import React, { useEffect, useRef, useState } from "react";
import { Box, Typography, Alert, AlertTitle } from "@mui/material";
import { styled } from "@mui/material/styles";
import { Apps, Warning } from "@mui/icons-material";
import { useLocation, useNavigate } from "react-router-dom";
import {
  getAppNameFromPath,
  isAppRootPath,
  getAppDefaultRoute,
  forceResetAppToHome,
} from "@/utils/microAppRouteManager";

const MicroAppWrapper = styled(Box)(({ theme }) => ({
  width: "100%",
  height: "100%",
  minHeight: "calc(100vh - 40px)",
  position: "relative",
  overflow: "hidden",
}));

const LoadingContainer = styled(Box)(({ theme }) => ({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "rgba(255, 255, 255, 0.9)",
  zIndex: 1000,
  color: theme.palette.text.secondary,
}));

/**
 * 微前端应用容器组件
 * 用于承载qiankun子应用
 */
const MicroAppContainer = ({
  appName,
  title = "微前端应用",
  error = null,
  ...props
}) => {
  const containerRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();

  // 路由状态管理
  useEffect(() => {
    const currentPath = location.pathname;
    const detectedAppName = getAppNameFromPath(currentPath);

    // 如果检测到的应用名称与传入的不匹配，使用检测到的
    const actualAppName = detectedAppName || appName;

    // 检查是否为应用根路径，如果是则重定向到默认首页
    if (isAppRootPath(currentPath)) {
      const defaultRoute = getAppDefaultRoute(actualAppName);
      // console.log(`🔄 检测到根路径访问，重定向到: ${defaultRoute}`);
      navigate(defaultRoute, { replace: true });
      return;
    }
  }, [location.pathname, appName, navigate]);

  useEffect(() => {
    // 启动qiankun（如果还没启动）
    if (window.__QIANKUN_START__) {
      window.__QIANKUN_START__();
    }

    // 确保容器存在
    if (containerRef.current) {
      // 添加一些样式确保子应用正确显示
      containerRef.current.style.width = "100%";
      containerRef.current.style.height = "100%";
      containerRef.current.style.overflow = "auto";

      // 监听子应用加载状态
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
            // 当子应用内容被添加到容器中时，关闭加载状态
            setLoading(false);
            observer.disconnect();
            break;
          }
        }
      });

      // 开始观察容器的变化
      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
      });

      // 设置超时，如果5秒后仍未加载完成，也关闭加载状态
      const timeout = setTimeout(() => {
        setLoading(false);
      }, 5000);

      return () => {
        observer.disconnect();
        clearTimeout(timeout);
      };
    }
  }, []);

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <MicroAppWrapper {...props}>
        <Alert severity="error" sx={{ m: 2 }}>
          <AlertTitle>微前端应用加载失败</AlertTitle>
          <Typography variant="body2">
            应用名称: {appName}
            <br />
            错误信息: {error.message || "未知错误"}
            <br />
            请检查子应用是否正常运行，或联系技术支持。
          </Typography>
        </Alert>
      </MicroAppWrapper>
    );
  }

  return (
    <MicroAppWrapper {...props}>
      {/* 加载状态 */}
      {loading && (
        <LoadingContainer>
          <Apps sx={{ fontSize: "3rem", mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" gutterBottom>
            正在加载 {title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            请稍候，微前端应用正在启动中...
          </Typography>
        </LoadingContainer>
      )}

      {/* 微前端应用容器 */}
      <Box
        ref={containerRef}
        id="sub-app-container"
        sx={{
          width: "100%",
          height: "100%",
          minHeight: "inherit",
          // 容器始终可见，但在加载时可能被加载提示覆盖
          display: "block",
          // 在加载时降低透明度，但不隐藏容器
          opacity: loading ? 0 : 1,
          transition: "opacity 0.3s ease-in-out",
        }}
      />
    </MicroAppWrapper>
  );
};

/**
 * 微前端路由配置辅助函数
 * 生成微前端应用的路由配置
 */
export const createMicroAppRoute = (appName, basePath, title) => ({
  path: `${basePath}/*`,
  element: <MicroAppContainer appName={appName} title={title} />,
  meta: {
    title: title,
    needLogin: false,
    hideLayout: false,
    isMicroApp: true,
  },
});

/**
 * 检查是否为微前端路由
 */
export const isMicroAppPath = (pathname) => {
  const microAppPaths = ["/cms-app", "/retail-ai-app", "/e-price-tag-app"];

  return microAppPaths.some((path) => pathname.startsWith(path));
};

/**
 * 获取微前端应用名称
 */
export const getMicroAppName = (pathname) => {
  if (pathname.startsWith("/cms-app")) return "cms-app";
  if (pathname.startsWith("/retail-ai-app")) return "retail-ai-app";
  if (pathname.startsWith("/e-price-tag-app")) return "e-price-tag-app";
  return null;
};

export default MicroAppContainer;
