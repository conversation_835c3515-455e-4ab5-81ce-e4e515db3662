// 自定义Service Worker
// 提供高级缓存策略和离线支持

const CACHE_NAME = 'zk-digimax-v1';
const STATIC_CACHE = 'static-cache-v1';
const DYNAMIC_CACHE = 'dynamic-cache-v1';
const API_CACHE = 'api-cache-v1';

// 需要预缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
];

// 缓存策略配置
const CACHE_STRATEGIES = {
  // 静态资源：缓存优先
  static: {
    pattern: /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/,
    strategy: 'CacheFirst',
    cacheName: STATIC_CACHE,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    maxEntries: 200
  },

  // API请求：网络优先
  api: {
    pattern: /\/api\//,
    strategy: 'NetworkFirst',
    cacheName: API_CACHE,
    maxAge: 24 * 60 * 60 * 1000, // 1天
    maxEntries: 100,
    networkTimeout: 5000
  },

  // 动态内容：网络优先，失败时使用缓存
  dynamic: {
    pattern: /\.(html|json)$/,
    strategy: 'NetworkFirst',
    cacheName: DYNAMIC_CACHE,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    maxEntries: 50,
    networkTimeout: 3000
  }
};

// Service Worker安装事件
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {

        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {

        return self.skipWaiting();
      })
      .catch((error) => {

      })
  );
});

// Service Worker激活事件
self.addEventListener('activate', (event) => {


  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // 删除旧版本的缓存
            if (cacheName !== STATIC_CACHE &&
              cacheName !== DYNAMIC_CACHE &&
              cacheName !== API_CACHE) {

              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {

        return self.clients.claim();
      })
  );
});

// 网络请求拦截
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // 只处理同源请求或特定的外部资源
  if (url.origin !== location.origin && !shouldCacheExternalResource(url)) {
    return;
  }

  // 根据请求类型选择缓存策略
  const strategy = getCacheStrategy(request);

  if (strategy) {
    event.respondWith(handleRequest(request, strategy));
  }
});

// 获取缓存策略
function getCacheStrategy(request) {
  const url = request.url;

  // API请求
  if (CACHE_STRATEGIES.api.pattern.test(url)) {
    return CACHE_STRATEGIES.api;
  }

  // 静态资源
  if (CACHE_STRATEGIES.static.pattern.test(url)) {
    return CACHE_STRATEGIES.static;
  }

  // 动态内容
  if (CACHE_STRATEGIES.dynamic.pattern.test(url)) {
    return CACHE_STRATEGIES.dynamic;
  }

  return null;
}

// 处理请求
async function handleRequest(request, strategy) {
  const cache = await caches.open(strategy.cacheName);

  switch (strategy.strategy) {
    case 'CacheFirst':
      return cacheFirst(request, cache, strategy);
    case 'NetworkFirst':
      return networkFirst(request, cache, strategy);
    case 'StaleWhileRevalidate':
      return staleWhileRevalidate(request, cache, strategy);
    default:
      return fetch(request);
  }
}

// 缓存优先策略
async function cacheFirst(request, cache, strategy) {
  try {
    const cachedResponse = await cache.match(request);

    if (cachedResponse && !isExpired(cachedResponse, strategy.maxAge)) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
      await cleanupCache(cache, strategy.maxEntries);
    }

    return networkResponse;
  } catch (error) {

    const cachedResponse = await cache.match(request);
    return cachedResponse || new Response('Network error', { status: 408 });
  }
}

// 网络优先策略
async function networkFirst(request, cache, strategy) {
  try {
    const networkResponse = await Promise.race([
      fetch(request),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Network timeout')), strategy.networkTimeout || 5000)
      )
    ]);

    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
      await cleanupCache(cache, strategy.maxEntries);
    }

    return networkResponse;
  } catch (error) {

    const cachedResponse = await cache.match(request);

    if (cachedResponse && !isExpired(cachedResponse, strategy.maxAge)) {
      return cachedResponse;
    }

    return new Response('Offline', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// 过期重新验证策略
async function staleWhileRevalidate(request, cache, strategy) {
  const cachedResponse = await cache.match(request);

  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
      cleanupCache(cache, strategy.maxEntries);
    }
    return networkResponse;
  }).catch(() => cachedResponse);

  return cachedResponse || fetchPromise;
}

// 检查缓存是否过期
function isExpired(response, maxAge) {
  if (!maxAge) return false;

  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;

  const responseTime = new Date(dateHeader).getTime();
  const now = Date.now();

  return (now - responseTime) > maxAge;
}

// 清理缓存，保持在最大条目数内
async function cleanupCache(cache, maxEntries) {
  if (!maxEntries) return;

  const keys = await cache.keys();

  if (keys.length > maxEntries) {
    const keysToDelete = keys.slice(0, keys.length - maxEntries);
    await Promise.all(keysToDelete.map(key => cache.delete(key)));
  }
}

// 判断是否应该缓存外部资源
function shouldCacheExternalResource(url) {
  const allowedDomains = [
    'fonts.googleapis.com',
    'fonts.gstatic.com',
    'api.map.baidu.com',
    'minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn'
  ];

  return allowedDomains.some(domain => url.hostname.includes(domain));
}

// 消息处理
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'GET_CACHE_INFO') {
    getCacheInfo().then(info => {
      event.ports[0].postMessage(info);
    });
  }
});

// 获取缓存信息
async function getCacheInfo() {
  const cacheNames = await caches.keys();
  const cacheInfo = {};

  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    cacheInfo[cacheName] = keys.length;
  }

  return cacheInfo;
}
