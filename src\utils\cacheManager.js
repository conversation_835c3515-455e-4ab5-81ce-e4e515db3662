/**
 * 缓存管理工具
 * 提供缓存监控、清理和管理功能
 */

class CacheManager {
  constructor() {
    this.isSupported = 'caches' in window;
    this.swRegistration = null;
  }

  /**
   * 初始化缓存管理器
   */
  async init() {
    if (!this.isSupported) {
      console.warn('Cache API not supported');
      return false;
    }

    try {
      // 注册Service Worker
      if ('serviceWorker' in navigator) {
        this.swRegistration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });
        
        console.log('Service Worker registered successfully');
        
        // 监听Service Worker更新
        this.swRegistration.addEventListener('updatefound', () => {
          console.log('Service Worker update found');
          this.handleServiceWorkerUpdate();
        });
      }
      
      return true;
    } catch (error) {
      console.error('Failed to register Service Worker:', error);
      return false;
    }
  }

  /**
   * 处理Service Worker更新
   */
  handleServiceWorkerUpdate() {
    const newWorker = this.swRegistration.installing;
    
    if (newWorker) {
      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          // 新的Service Worker已安装，提示用户刷新
          this.notifyUpdate();
        }
      });
    }
  }

  /**
   * 通知用户有更新
   */
  notifyUpdate() {
    if (window.confirm('应用有新版本可用，是否立即更新？')) {
      this.skipWaiting();
    }
  }

  /**
   * 跳过等待，立即激活新的Service Worker
   */
  skipWaiting() {
    if (this.swRegistration && this.swRegistration.waiting) {
      this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }

  /**
   * 获取所有缓存信息
   */
  async getCacheInfo() {
    if (!this.isSupported) return {};

    try {
      const cacheNames = await caches.keys();
      const cacheInfo = {};

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        
        cacheInfo[cacheName] = {
          count: keys.length,
          urls: keys.map(request => request.url),
          size: await this.calculateCacheSize(cache, keys)
        };
      }

      return cacheInfo;
    } catch (error) {
      console.error('Failed to get cache info:', error);
      return {};
    }
  }

  /**
   * 计算缓存大小
   */
  async calculateCacheSize(cache, keys) {
    let totalSize = 0;

    for (const request of keys) {
      try {
        const response = await cache.match(request);
        if (response) {
          const blob = await response.blob();
          totalSize += blob.size;
        }
      } catch (error) {
        console.warn('Failed to calculate size for:', request.url);
      }
    }

    return totalSize;
  }

  /**
   * 格式化缓存大小
   */
  formatCacheSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 清理指定缓存
   */
  async clearCache(cacheName) {
    if (!this.isSupported) return false;

    try {
      const deleted = await caches.delete(cacheName);
      console.log(`Cache ${cacheName} ${deleted ? 'deleted' : 'not found'}`);
      return deleted;
    } catch (error) {
      console.error(`Failed to delete cache ${cacheName}:`, error);
      return false;
    }
  }

  /**
   * 清理所有缓存
   */
  async clearAllCaches() {
    if (!this.isSupported) return false;

    try {
      const cacheNames = await caches.keys();
      const deletePromises = cacheNames.map(cacheName => caches.delete(cacheName));
      
      await Promise.all(deletePromises);
      console.log('All caches cleared');
      return true;
    } catch (error) {
      console.error('Failed to clear all caches:', error);
      return false;
    }
  }

  /**
   * 预缓存资源
   */
  async precacheResources(urls) {
    if (!this.isSupported) return false;

    try {
      const cache = await caches.open('precache-v1');
      await cache.addAll(urls);
      console.log('Resources precached successfully');
      return true;
    } catch (error) {
      console.error('Failed to precache resources:', error);
      return false;
    }
  }

  /**
   * 检查资源是否已缓存
   */
  async isResourceCached(url) {
    if (!this.isSupported) return false;

    try {
      const response = await caches.match(url);
      return !!response;
    } catch (error) {
      console.error('Failed to check cache for:', url);
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    const cacheInfo = await this.getCacheInfo();
    
    let totalEntries = 0;
    let totalSize = 0;
    
    Object.values(cacheInfo).forEach(info => {
      totalEntries += info.count;
      totalSize += info.size;
    });

    return {
      totalCaches: Object.keys(cacheInfo).length,
      totalEntries,
      totalSize: this.formatCacheSize(totalSize),
      caches: cacheInfo
    };
  }

  /**
   * 监控缓存性能
   */
  startCacheMonitoring() {
    if (!this.isSupported) return;

    // 监控缓存命中率
    let cacheHits = 0;
    let cacheMisses = 0;

    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const request = new Request(...args);
      const cachedResponse = await caches.match(request);
      
      if (cachedResponse) {
        cacheHits++;
        console.log(`Cache HIT: ${request.url}`);
      } else {
        cacheMisses++;
        console.log(`Cache MISS: ${request.url}`);
      }

      return originalFetch(...args);
    };

    // 定期报告缓存统计
    setInterval(() => {
      const total = cacheHits + cacheMisses;
      if (total > 0) {
        const hitRate = ((cacheHits / total) * 100).toFixed(2);
        console.log(`Cache Hit Rate: ${hitRate}% (${cacheHits}/${total})`);
      }
    }, 60000); // 每分钟报告一次
  }

  /**
   * 导出缓存配置
   */
  async exportCacheConfig() {
    const stats = await this.getCacheStats();
    const config = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      cacheStats: stats,
      serviceWorkerState: this.swRegistration?.active?.state || 'not-registered'
    };

    return JSON.stringify(config, null, 2);
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager();

// 自动初始化
if (typeof window !== 'undefined') {
  cacheManager.init().then(success => {
    if (success) {
      console.log('Cache Manager initialized successfully');
      
      // 在开发环境下启用缓存监控
      if (process.env.NODE_ENV === 'development') {
        cacheManager.startCacheMonitoring();
      }
    }
  });
}

export default cacheManager;
