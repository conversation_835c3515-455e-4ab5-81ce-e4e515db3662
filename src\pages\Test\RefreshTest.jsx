/**
 * 刷新测试页面
 * 用于测试强制刷新功能
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  AlertTitle,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

const RefreshTest = () => {
  const navigate = useNavigate();
  const [pageLoadTime, setPageLoadTime] = useState(null);
  const [sessionData, setSessionData] = useState({});
  const [refreshCount, setRefreshCount] = useState(0);

  useEffect(() => {
    // 记录页面加载时间
    setPageLoadTime(new Date().toLocaleTimeString());
    
    // 获取会话数据
    const storedRefreshCount = parseInt(sessionStorage.getItem('refreshCount') || '0');
    const newRefreshCount = storedRefreshCount + 1;
    setRefreshCount(newRefreshCount);
    sessionStorage.setItem('refreshCount', newRefreshCount.toString());
    
    // 记录页面访问历史
    const visitHistory = JSON.parse(sessionStorage.getItem('visitHistory') || '[]');
    visitHistory.push({
      path: window.location.pathname,
      timestamp: Date.now(),
      loadTime: new Date().toLocaleTimeString()
    });
    
    // 保持最近10条记录
    if (visitHistory.length > 10) {
      visitHistory.shift();
    }
    
    sessionStorage.setItem('visitHistory', JSON.stringify(visitHistory));
    setSessionData({ visitHistory });
  }, []);

  const handleNavigateToMicroApp = (appPath) => {
    navigate(appPath);
  };

  const handleClearSession = () => {
    sessionStorage.removeItem('refreshCount');
    sessionStorage.removeItem('visitHistory');
    setRefreshCount(0);
    setSessionData({ visitHistory: [] });
  };

  const testScenarios = [
    {
      title: '测试场景1：CMS应用',
      description: '跳转到CMS应用，然后使用浏览器后退按钮返回',
      path: '/cms-app/dashboard/summary',
      color: 'primary'
    },
    {
      title: '测试场景2：零售AI应用',
      description: '跳转到零售AI应用，然后使用浏览器后退按钮返回',
      path: '/retail-ai-app/dashboard',
      color: 'secondary'
    },
    {
      title: '测试场景3：电子价签应用',
      description: '跳转到电子价签应用，然后使用浏览器后退按钮返回',
      path: '/e-price-tag-app/dashboard',
      color: 'success'
    }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        强制刷新功能测试页面
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <AlertTitle>测试说明</AlertTitle>
        <Typography variant="body2">
          1. 点击下方按钮跳转到微前端应用<br/>
          2. 在微前端应用中浏览一些页面<br/>
          3. 使用浏览器的后退按钮返回到主应用<br/>
          4. 观察页面是否被强制刷新（页面加载时间和刷新计数会更新）<br/>
          5. 如果启用了强制刷新功能，返回主应用时页面会自动刷新
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* 页面状态信息 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                页面状态信息
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="页面加载时间"
                    secondary={pageLoadTime}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="刷新计数"
                    secondary={
                      <Chip
                        label={refreshCount}
                        color={refreshCount > 1 ? 'warning' : 'default'}
                        size="small"
                      />
                    }
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="当前路径"
                    secondary={window.location.pathname}
                  />
                </ListItem>
              </List>
              <Button
                variant="outlined"
                color="error"
                onClick={handleClearSession}
                size="small"
                sx={{ mt: 2 }}
              >
                清除会话数据
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* 测试场景 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                测试场景
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {testScenarios.map((scenario, index) => (
                  <Box key={index} sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      {scenario.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {scenario.description}
                    </Typography>
                    <Button
                      variant="contained"
                      color={scenario.color}
                      onClick={() => handleNavigateToMicroApp(scenario.path)}
                      size="small"
                    >
                      开始测试
                    </Button>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 访问历史 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                访问历史
              </Typography>
              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                <List dense>
                  {sessionData.visitHistory && sessionData.visitHistory.length > 0 ? (
                    sessionData.visitHistory.slice().reverse().map((visit, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={visit.path}
                          secondary={`加载时间: ${visit.loadTime} | 时间戳: ${new Date(visit.timestamp).toLocaleString()}`}
                        />
                      </ListItem>
                    ))
                  ) : (
                    <ListItem>
                      <ListItemText
                        primary="暂无访问历史"
                        secondary="开始导航以查看访问记录"
                      />
                    </ListItem>
                  )}
                </List>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Alert severity="success" sx={{ mt: 3 }}>
        <AlertTitle>预期行为</AlertTitle>
        <Typography variant="body2">
          如果强制刷新功能正常工作：<br/>
          1. 从微前端应用使用浏览器后退返回时，页面会自动刷新<br/>
          2. 页面加载时间会更新为新的时间<br/>
          3. 刷新计数会增加<br/>
          4. 访问历史中会记录新的访问记录<br/>
          <br/>
          如果没有启用强制刷新功能，返回时页面不会刷新，这些数据保持不变。
        </Typography>
      </Alert>
    </Box>
  );
};

export default RefreshTest;
