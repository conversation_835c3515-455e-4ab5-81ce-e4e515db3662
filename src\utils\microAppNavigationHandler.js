/**
 * 微前端导航处理器
 * 处理浏览器前进后退导航，确保微前端应用路由状态正确
 */

import {
  getAppNameFromPath,
  isAppRootPath,
  getAppDefaultRoute,
  saveAppRouteState,
  clearAppRouteState
} from './microAppRouteManager';

// 导航配置
const NAVIGATION_CONFIG = {
  // 是否在从微前端返回主应用时强制刷新页面
  forceRefreshOnReturnToMain: true,
  // 刷新延迟时间（毫秒）
  refreshDelay: 100,
  // 路径恢复延迟时间（毫秒）
  restoreDelay: 100
};

// 导航状态管理
let isNavigationHandlerActive = false;
let lastKnownPath = '';
let navigationHistory = [];
let lastMicroAppPath = ''; // 记录上次访问的微前端路径

/**
 * 记录导航历史
 * @param {string} path - 路径
 * @param {string} action - 动作类型 (PUSH, REPLACE, POP)
 */
const recordNavigation = (path, action = 'UNKNOWN') => {
  const record = {
    path,
    action,
    timestamp: Date.now(),
    appName: getAppNameFromPath(path)
  };

  navigationHistory.push(record);

  // 保持历史记录在合理范围内
  if (navigationHistory.length > 50) {
    navigationHistory.shift();
  }

  // console.log(`📍 导航记录: ${action} -> ${path}`, record);
};

/**
 * 获取导航历史
 * @returns {Array} 导航历史记录
 */
export const getNavigationHistory = () => {
  return [...navigationHistory];
};

/**
 * 清除导航历史
 */
export const clearNavigationHistory = () => {
  navigationHistory = [];
};

/**
 * 检查是否需要刷新页面
 * @param {string} newPath - 新路径
 * @param {string} oldPath - 旧路径
 * @param {string} action - 动作类型
 * @returns {boolean} 是否需要刷新
 */
const shouldRefreshPage = (newPath, oldPath, action) => {
  // 如果配置禁用了强制刷新，直接返回false
  if (!NAVIGATION_CONFIG.forceRefreshOnReturnToMain) {
    return false;
  }

  const newAppName = getAppNameFromPath(newPath);
  const oldAppName = getAppNameFromPath(oldPath);

  // 只在浏览器后退/前进时检查
  if (action !== 'POP') {
    return false;
  }

  // 如果从微前端应用返回到主应用，需要刷新
  if (oldAppName && !newAppName) {
    // console.log(`🔄 从微前端应用 ${oldAppName} 返回主应用，需要刷新页面`);
    return true;
  }

  return false;
};

/**
 * 处理路径变化
 * @param {string} newPath - 新路径
 * @param {string} action - 动作类型
 */
const handlePathChange = (newPath, action = 'POP') => {
  if (newPath === lastKnownPath) {
    return; // 路径没有变化，跳过处理
  }

  const oldPath = lastKnownPath;
  lastKnownPath = newPath;

  recordNavigation(newPath, action);

  const newAppName = getAppNameFromPath(newPath);
  const oldAppName = getAppNameFromPath(oldPath);

  // console.log(`🔄 路径变化: ${oldPath} -> ${newPath}`, {
  //   oldAppName,
  //   newAppName,
  //   action,
  //   isRootPath: isAppRootPath(newPath)
  // });

  // 检查是否需要刷新页面
  if (shouldRefreshPage(newPath, oldPath, action)) {
    // console.log(`🔄 触发页面刷新: ${oldPath} -> ${newPath}`);

    // 保存当前路径到sessionStorage，刷新后恢复
    sessionStorage.setItem('microapp_target_path', newPath);

    // 延迟刷新，确保路径变化已经完成
    setTimeout(() => {
      window.location.reload();
    }, NAVIGATION_CONFIG.refreshDelay);

    return;
  }

  // 更新微前端路径记录
  if (newAppName) {
    lastMicroAppPath = newPath;
  }

  // 如果从一个微前端应用切换到另一个应用
  if (oldAppName && newAppName && oldAppName !== newAppName) {
    // console.log(`🔄 应用切换: ${oldAppName} -> ${newAppName}`);

    // 保存旧应用的路由状态
    if (oldPath) {
      saveAppRouteState(oldAppName, oldPath);
    }

    // 如果新路径是根路径，需要重定向到默认首页
    if (isAppRootPath(newPath)) {
      const defaultRoute = getAppDefaultRoute(newAppName);
      // console.log(`🏠 重定向到默认首页: ${defaultRoute}`);

      // 使用 replaceState 避免在历史记录中添加额外条目
      window.history.replaceState(null, '', defaultRoute);
      lastKnownPath = defaultRoute;
      recordNavigation(defaultRoute, 'REPLACE');
      return;
    }
  }

  // 如果是微前端应用的根路径访问
  if (newAppName && isAppRootPath(newPath)) {
    const defaultRoute = getAppDefaultRoute(newAppName);
    // console.log(`🏠 根路径访问，重定向到: ${defaultRoute}`);

    window.history.replaceState(null, '', defaultRoute);
    lastKnownPath = defaultRoute;
    recordNavigation(defaultRoute, 'REPLACE');
    return;
  }

  // 保存当前应用的路由状态
  if (newAppName) {
    saveAppRouteState(newAppName, newPath);
  }
};

/**
 * popstate 事件处理器
 * @param {PopStateEvent} event - popstate 事件
 */
const handlePopState = (event) => {
  const currentPath = window.location.pathname;
  // console.log(`⬅️ 浏览器后退/前进: ${currentPath}`, event.state);

  handlePathChange(currentPath, 'POP');
};

/**
 * 监听 pushState 和 replaceState
 */
const wrapHistoryMethods = () => {
  const originalPushState = window.history.pushState;
  const originalReplaceState = window.history.replaceState;

  window.history.pushState = function (state, title, url) {
    const result = originalPushState.apply(this, arguments);
    if (url) {
      const newPath = typeof url === 'string' ? url : url.pathname;
      handlePathChange(newPath, 'PUSH');
    }
    return result;
  };

  window.history.replaceState = function (state, title, url) {
    const result = originalReplaceState.apply(this, arguments);
    if (url) {
      const newPath = typeof url === 'string' ? url : url.pathname;
      handlePathChange(newPath, 'REPLACE');
    }
    return result;
  };

  // 保存原始方法的引用，用于恢复
  window.history._originalPushState = originalPushState;
  window.history._originalReplaceState = originalReplaceState;
};

/**
 * 恢复原始的 history 方法
 */
const unwrapHistoryMethods = () => {
  if (window.history._originalPushState) {
    window.history.pushState = window.history._originalPushState;
    delete window.history._originalPushState;
  }

  if (window.history._originalReplaceState) {
    window.history.replaceState = window.history._originalReplaceState;
    delete window.history._originalReplaceState;
  }
};

/**
 * 启动微前端导航处理器
 */
export const startMicroAppNavigationHandler = () => {
  if (isNavigationHandlerActive) {
    // console.log('🔄 微前端导航处理器已经启动');
    return;
  }

  // console.log('🚀 启动微前端导航处理器');

  // 检查是否有需要恢复的目标路径（页面刷新后）
  const targetPath = sessionStorage.getItem('microapp_target_path');
  if (targetPath && targetPath !== window.location.pathname) {
    // console.log(`🔄 页面刷新后恢复目标路径: ${targetPath}`);
    sessionStorage.removeItem('microapp_target_path');

    // 延迟导航，确保应用完全加载
    setTimeout(() => {
      window.history.replaceState(null, '', targetPath);
      lastKnownPath = targetPath;
      recordNavigation(targetPath, 'RESTORE');
    }, NAVIGATION_CONFIG.restoreDelay);
  } else {
    // 记录当前路径
    lastKnownPath = window.location.pathname;
    recordNavigation(lastKnownPath, 'INIT');
  }

  // 监听 popstate 事件（浏览器前进后退）
  window.addEventListener('popstate', handlePopState);

  // 包装 history 方法以监听程序化导航
  wrapHistoryMethods();

  isNavigationHandlerActive = true;

  // 检查当前路径是否需要重定向
  const currentPath = window.location.pathname;
  const appName = getAppNameFromPath(currentPath);
  if (appName && isAppRootPath(currentPath)) {
    const defaultRoute = getAppDefaultRoute(appName);
    // console.log(`🏠 启动时检测到根路径，重定向到: ${defaultRoute}`);
    window.history.replaceState(null, '', defaultRoute);
    lastKnownPath = defaultRoute;
    recordNavigation(defaultRoute, 'REPLACE');
  }
};

/**
 * 停止微前端导航处理器
 */
export const stopMicroAppNavigationHandler = () => {
  if (!isNavigationHandlerActive) {
    return;
  }

  // console.log('🛑 停止微前端导航处理器');

  // 移除事件监听器
  window.removeEventListener('popstate', handlePopState);

  // 恢复原始的 history 方法
  unwrapHistoryMethods();

  isNavigationHandlerActive = false;
};

/**
 * 检查导航处理器是否活跃
 * @returns {boolean} 是否活跃
 */
export const isNavigationHandlerRunning = () => {
  return isNavigationHandlerActive;
};

/**
 * 手动触发路径检查
 * @param {string} path - 要检查的路径（可选，默认使用当前路径）
 */
export const checkCurrentPath = (path = window.location.pathname) => {
  handlePathChange(path, 'CHECK');
};

/**
 * 更新导航配置
 * @param {Object} config - 配置对象
 */
export const updateNavigationConfig = (config) => {
  Object.assign(NAVIGATION_CONFIG, config);
  // console.log('🔧 导航配置已更新:', NAVIGATION_CONFIG);
};

/**
 * 获取当前导航配置
 * @returns {Object} 当前配置
 */
export const getNavigationConfig = () => {
  return { ...NAVIGATION_CONFIG };
};

/**
 * 启用/禁用强制刷新功能
 * @param {boolean} enabled - 是否启用
 */
export const setForceRefreshEnabled = (enabled) => {
  NAVIGATION_CONFIG.forceRefreshOnReturnToMain = enabled;
  // console.log(`🔧 强制刷新功能已${enabled ? '启用' : '禁用'}`);
};

// 导出默认对象
export default {
  startMicroAppNavigationHandler,
  stopMicroAppNavigationHandler,
  isNavigationHandlerRunning,
  checkCurrentPath,
  getNavigationHistory,
  clearNavigationHistory,
  updateNavigationConfig,
  getNavigationConfig,
  setForceRefreshEnabled
};
