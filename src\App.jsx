import { useEffect, useState, useCallback } from "react";
import { registerMicroApps, start } from "qiankun";
import { ToastContainer } from "react-toastify";

// 样式和国际化
import "@/lang/index";
import "@/styles/global.less";

// 组件导入
import RouterWaiter from "./components/routerWaiter";
import ThemeCustomization from "./themes";
import { ConfirmProvider } from "./components/zkconfirm";
import ScrollTop from "@c/ScrollTop";
import ErrorBoundary from "./router/ExceptionComponent/ErrorBoundary";

// 路由和工具
import routes from "./router/routers";
import onRouteBefore from "./router/onRouteBefore";
import { initGlobalErrorHandler } from "./utils/globalErrorHandler";

// 微前端配置导入
import {
  createMicroAppConfig,
  QIANKUN_CONFIG,
  TOAST_CONFIG,
  checkEnvironmentConfig,
} from "./config/microAppConfig";

const App = () => {
  const [qiankunStarted, setQiankunStarted] = useState(false);

  // 注册微前端应用
  const registerMicroAppsWithConfig = useCallback(() => {
    try {
      if (!checkEnvironmentConfig()) {
        return false;
      }

      const microAppsConfig = createMicroAppConfig();
      registerMicroApps(microAppsConfig);

      // 启动qiankun
      start(QIANKUN_CONFIG);
      return true;
    } catch (error) {
      // console.error("微前端应用注册失败:", error);
      // 即使注册失败也不跳转到500页面，让主应用继续运行
      return false;
    }
  }, []);

  // 全局启动qiankun的函数
  const startQiankun = useCallback(() => {
    if (!qiankunStarted) {
      const success = registerMicroAppsWithConfig();
      if (success) {
        setQiankunStarted(true);
        // console.log("✅ Qiankun started successfully");
      }
    }
  }, [qiankunStarted, registerMicroAppsWithConfig]);

  // 将启动函数挂载到全局，供MicroAppContainer使用
  useEffect(() => {
    window.__QIANKUN_START__ = startQiankun;
    return () => {
      delete window.__QIANKUN_START__;
    };
  }, [startQiankun]);

  useEffect(() => {
    // 初始化全局错误处理器
    initGlobalErrorHandler();
  }, []);

  return (
    <ErrorBoundary>
      <ThemeCustomization>
        <ConfirmProvider>
          <ScrollTop>
            <RouterWaiter routes={routes} onRouteBefore={onRouteBefore} />
            <ToastContainer {...TOAST_CONFIG} />
          </ScrollTop>
        </ConfirmProvider>
      </ThemeCustomization>
    </ErrorBoundary>
  );
};

export default App;
