/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/rules-of-hooks */
/**
 * @param {string} pathname 当前路由路径
 * @param {object} meta 当前路由自定义meta字段
 * @return {string} 需要跳转到其他页时，就返回一个该页的path路径，或返回resolve该路径的promise对象
 */
import { getToken } from "@/utils/auth";
import { getLoginInfor } from "@/service/api/user";
import { useDispatch, useSelector } from "react-redux";
import { getMenuList } from "@s/api/premission";
import { store } from "@/store";
import { setMenuList } from "@/store/reducers/menu";
import i18n from "i18next";
import { setInfoLoaded } from "@/store/reducers/user";
import { useDispatchUser } from "@/hooks/user.js";
import { isMicroAppPath } from "@/components/MicroAppContainer";
import { toast } from 'react-toastify';
import { ensureAppListLoaded } from "@/utils/appListManager";
import {
  validateMicroAppAccess,
  getAppNameFromPath
} from "@/utils/subscriptionAccessControl";
const flattenTree = (nodes) => {
  let result = [];
  nodes.forEach((node) => {
    result.push(node.id);
    if (node.children) {
      let ids = flattenTree(node.children);
      result = [...result, ...ids];
    }
  });
  return result;
};




const onRouteBefore = async ({ pathname, meta }) => {
  // 检查微前端路由的订阅状态
  if (pathname.startsWith('/cms-app') ||
    pathname.startsWith('/retail-ai-app') ||
    pathname.startsWith('/e-price-tag-app')) {

    // 获取用户信息和应用列表
    const state = store.getState();
    const userInfo = state.user.userInfor;

    // 确保应用列表已加载
    let appList = userInfo?.useAppList;

    if (!appList || !Array.isArray(appList) || appList.length === 0) {
      // console.log('📋 应用列表未加载，开始获取...');
      try {
        appList = await ensureAppListLoaded();
      } catch (error) {
        // console.error('❌ 获取应用列表失败:', error);
        appList = [];
      }
    }

    // console.log('🔍 检查微前端访问权限:', {
    //   pathname,
    //   userInfo: !!userInfo,
    //   appListLength: appList?.length || 0,
    //   appList: appList
    // });

    // 验证订阅状态
    const accessResult = validateMicroAppAccess(pathname, appList);

    if (!accessResult.allowed) {
      const appName = getAppNameFromPath(pathname);

      if (accessResult.reason === 'not_subscribed') {
        // console.warn(`❌ 访问被拒绝: 用户未订阅 ${appName}`);
        // toast.error(`您尚未订阅 ${appName}，请先订阅后再访问`);
        return "/subscription-required";
      } else {
        // console.warn(`❌ 访问被拒绝: ${accessResult.reason}`);
        toast.error(`无法访问 ${appName}`);
        return "/404";
      }
    }

    // console.log(`✅ 微前端访问权限验证通过: ${accessResult.appName}`);
    return;
  }

  // 如果是错误页面，直接放行
  if (pathname === '/404' || pathname === '/403' ||
    pathname === '/500' || pathname === '/network-error') {
    return;
  }

  // 动态修改页面标题
  if (meta.i18n) {
    document.title = i18n.t("menu." + meta.i18n);
  }

  const { isInfoLoaded, userInfor } = store.getState().user;
  const dispatch = useDispatch();

  const { stateSetPermission, stateSetUser } = useDispatchUser()

  // 示例：动态修改页面title
  if (meta.i18n !== undefined) {
    document.title = i18n.t("menu." + meta?.i18n);
  }

  try {
    // 示例：判断未登录跳转登录页
    if (meta.id || meta.idEditor) {
      if (!meta.idEditor && !getToken()) {
        return "/login";
      } else {
        if (!userInfor || !isInfoLoaded) {
          try {
            const [userRes, menuRes] = await Promise.all([
              getLoginInfor(),
              getMenuList({ applicationCode: "L3" }),
            ]);

            if (userRes?.data && menuRes?.data) {
              dispatch(stateSetUser(userRes.data));
              dispatch(stateSetPermission(userRes?.data?.permissions));
              dispatch(setMenuList(menuRes.data));
              dispatch(setInfoLoaded(true));

              // 成功加载用户信息和菜单后，不做任何跳转，让路由正常进行
              return;
            } else {
              return "/login";
            }
          } catch (err) {
            // console.error("Error fetching user info or menus:", err);
            // 不要直接跳转到500页面，而是返回登录页
            toast.error("获取用户信息失败，请重新登录");
            return "/login";
          }
        }
      }
    }
  } catch (error) {
    // console.error("路由守卫错误:", error);
    // 不要直接跳转到500页面，而是返回登录页
    toast.error("路由守卫错误，请重新登录");
    return "/login";
  }
};
export default onRouteBefore;
