/**
 * 微前端调试页面
 * 用于测试和调试微前端路由状态管理
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
  Grid,
  Alert,
  AlertTitle,
  Switch,
  FormControlLabel
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  getAppNameFromPath,
  isAppRootPath,
  getAppDefaultRoute,
  forceResetAppToHome
} from '@/utils/microAppRouteManager';
import {
  getNavigationHistory,
  clearNavigationHistory,
  checkCurrentPath,
  getNavigationConfig,
  setForceRefreshEnabled
} from '@/utils/microAppNavigationHandler';

const MicroAppDebug = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [navigationHistory, setNavigationHistory] = useState([]);
  const [currentAppInfo, setCurrentAppInfo] = useState({});
  const [config, setConfig] = useState({});

  // 更新当前应用信息
  useEffect(() => {
    const currentPath = location.pathname;
    const appName = getAppNameFromPath(currentPath);
    const isRoot = isAppRootPath(currentPath);
    const defaultRoute = appName ? getAppDefaultRoute(appName) : null;

    setCurrentAppInfo({
      currentPath,
      appName,
      isRoot,
      defaultRoute
    });
  }, [location.pathname]);

  // 定期更新导航历史和配置
  useEffect(() => {
    const updateData = () => {
      setNavigationHistory(getNavigationHistory());
      setConfig(getNavigationConfig());
    };

    updateData();
    const interval = setInterval(updateData, 1000);

    return () => clearInterval(interval);
  }, []);

  // 测试路由
  const testRoutes = [
    { path: '/cms-app', label: 'CMS应用根路径' },
    { path: '/cms-app/dashboard/summary', label: 'CMS应用首页' },
    { path: '/cms-app/users', label: 'CMS用户管理' },
    { path: '/retail-ai-app', label: '零售AI应用根路径' },
    { path: '/retail-ai-app/dashboard', label: '零售AI应用首页' },
    { path: '/e-price-tag-app', label: '电子价签应用根路径' },
    { path: '/e-price-tag-app/dashboard', label: '电子价签应用首页' },
    { path: '/', label: '主应用首页' },
    { path: '/dashboard', label: '主应用仪表板' }
  ];

  const handleNavigate = (path) => {
    navigate(path);
  };

  const handleForceReset = (appName) => {
    forceResetAppToHome(appName);
    window.location.reload(); // 强制刷新以查看效果
  };

  const handleClearHistory = () => {
    clearNavigationHistory();
    setNavigationHistory([]);
  };

  const handleCheckCurrentPath = () => {
    checkCurrentPath();
  };

  const handleToggleForceRefresh = (event) => {
    const enabled = event.target.checked;
    setForceRefreshEnabled(enabled);
    setConfig(prev => ({ ...prev, forceRefreshOnReturnToMain: enabled }));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        微前端路由调试工具
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <AlertTitle>使用说明</AlertTitle>
        <Typography variant="body2">
          1. 点击下方按钮测试不同路由的跳转<br/>
          2. 使用浏览器前进后退按钮测试路由状态管理<br/>
          3. 观察导航历史记录和当前应用信息的变化<br/>
          4. 测试从主应用跳转到子应用，再回退到主应用，然后再次进入子应用的场景<br/>
          5. 启用"强制刷新"功能后，从微前端返回主应用时会自动刷新页面
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* 配置选项 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                配置选项
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={config.forceRefreshOnReturnToMain || false}
                    onChange={handleToggleForceRefresh}
                  />
                }
                label="从微前端返回主应用时强制刷新页面"
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                启用此选项后，当用户使用浏览器后退从微前端应用返回主应用时，会自动刷新页面以确保状态完全重置。
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* 当前应用信息 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                当前应用信息
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="当前路径"
                    secondary={currentAppInfo.currentPath}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="应用名称"
                    secondary={currentAppInfo.appName || '主应用'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="是否为根路径"
                    secondary={
                      <Chip
                        label={currentAppInfo.isRoot ? '是' : '否'}
                        color={currentAppInfo.isRoot ? 'warning' : 'success'}
                        size="small"
                      />
                    }
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="默认路由"
                    secondary={currentAppInfo.defaultRoute || '无'}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* 测试路由 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                测试路由
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {testRoutes.map((route) => (
                  <Button
                    key={route.path}
                    variant={location.pathname === route.path ? 'contained' : 'outlined'}
                    onClick={() => handleNavigate(route.path)}
                    size="small"
                  >
                    {route.label}
                  </Button>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 调试操作 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                调试操作
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="outlined"
                  color="warning"
                  onClick={() => handleForceReset('cms-app')}
                >
                  强制重置CMS应用到首页
                </Button>
                <Button
                  variant="outlined"
                  color="warning"
                  onClick={() => handleForceReset('retail-ai-app')}
                >
                  强制重置零售AI应用到首页
                </Button>
                <Button
                  variant="outlined"
                  color="warning"
                  onClick={() => handleForceReset('e-price-tag-app')}
                >
                  强制重置电子价签应用到首页
                </Button>
                <Divider sx={{ my: 1 }} />
                <Button
                  variant="outlined"
                  onClick={handleCheckCurrentPath}
                >
                  检查当前路径
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={handleClearHistory}
                >
                  清除导航历史
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 导航历史 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                导航历史 ({navigationHistory.length})
              </Typography>
              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                <List dense>
                  {navigationHistory.slice(-10).reverse().map((record, index) => (
                    <React.Fragment key={record.timestamp}>
                      <ListItem>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip
                                label={record.action}
                                size="small"
                                color={
                                  record.action === 'POP' ? 'warning' :
                                  record.action === 'PUSH' ? 'primary' :
                                  record.action === 'REPLACE' ? 'secondary' :
                                  record.action === 'RESTORE' ? 'success' : 'default'
                                }
                              />
                              <Typography variant="body2">
                                {record.path}
                              </Typography>
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="caption">
                                应用: {record.appName || '主应用'}
                              </Typography>
                              <br />
                              <Typography variant="caption">
                                时间: {new Date(record.timestamp).toLocaleTimeString()}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < navigationHistory.slice(-10).length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                  {navigationHistory.length === 0 && (
                    <ListItem>
                      <ListItemText
                        primary="暂无导航历史"
                        secondary="开始导航以查看历史记录"
                      />
                    </ListItem>
                  )}
                </List>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MicroAppDebug;
