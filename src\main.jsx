import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.jsx";
import { BrowserRouter as Router } from "react-router-dom";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "./store";
import "@/styles/simplebar.css";
// import "./rem.js";
import "virtual:svg-icons-register";

// 开发环境下引入应用列表管理器
if (import.meta.env.DEV) {
  import("@/utils/appListManager");
}

createRoot(document.getElementById("root")).render(
  // <StrictMode>
  <ReduxProvider store={store}>
    <Router>
      <App />
    </Router>
  </ReduxProvider>
  // </StrictMode>
);
