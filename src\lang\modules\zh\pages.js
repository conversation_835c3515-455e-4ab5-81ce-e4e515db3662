const pages = {
  home: {
    welcome: "欢迎",
    your_app: "您的应用",
    other_app: "探索其他应用",
    download_mobile: "在您的手机上下载我们的软件！",
    find_madia: "在社交媒体上找到我们！",
    screenList_item_0: "兼容所有操作系统",
    screenList_item_1: "基于云端",
    screenList_item_2: "可用于数字标牌、LED和LCD屏幕",

    nuTagList_item_1: "可与任何POS系统集成",
    nuTagList_item_2: "基于云端",
    nuTagList_item_3: "可扩展",
    nuTagList_item_4: "实时和定时更新",
    nuTagList_item_5: "可定制模板",

    zataList_item_1: "高精度",
    zataList_item_2: "基于云端",
    zataList_item_3: "实时监控和报告",
    zataList_item_4: "AI驱动优化",
    zataList_item_5: "隐私和合规",
  },
  partnerEmployee: {
    "operation.delete": "删除代理商用户"
  },
  appCenter: {
    title: "应用中心",
    cms_content: "随时随地创建、管理和更新屏幕内容",
    nutag_content: "远程管理电子价签上的价格、促销和各种其他内容",
    zata_content: "先进的AI驱动数据处理软件，可准确识别性别、年龄和属性",
    open: "打开",
    subscribe: "订阅",
  },
  system_setting: {},
  branch: {
    name: "名称",
    region: "租户区域",
    email: "租户所有者邮箱",
    mobile: "租户所有者手机",
    title: "租户",
    add_branch: "新增租户",
    edit_branch: "编辑租户",
    delete_branch: "删除确认",
    delete_content: "您确定要永久删除这个账号吗？",
    view_branch: "查看租户",
    enter_name: "请输入名称",
    branch_region: "租户区域",
    branch_region_required: "租户区域是必填项",
    branch_email: "邮箱",
    branch_email_required: "请输入邮箱",
    branch_mobile: "租户电话",
  },

  branch_user: {
    title: "租户用户",
    logo: "公司标志",
    firstName: "名",
    lastName: "姓",
    email: "电子邮箱地址",
    phone: "手机号码",
    roleNames: "授权级别",
    enter_firstName: "请输入名称",
    enter_lastName: "请输入姓氏",

    enter_email: "请输入电子邮箱",
    add_title: "添加租户员工",
    edit_title: "编辑租户员工",
    auth_level: "授权级别 *",
    view_branch: "查看租户信息",
    region: "区域",
    owner_email: "组织所有者邮箱",
    owner_mobile: "组织所有者手机",
    address: "地址",
    area_name: "区域名称",
  },
  retail: {},
  parter: {},
  system_log: {},
  data_permission: {
    title: "数据权限",
    name: "权限名称",
    type: "权限类型",
    add_title: "添加数据权限",
    edit_title: "编辑数据权限",
    enter_name: "请输入权限名称",
    name_required: "权限名称是必填项",
    select_type: "请选择权限等级",
    add_permission: "负责权限",
    this_level: "是否选择此级别及以下所有级别权限？",
    email: "电子邮箱",
    select_outlets: "选择门店",
    sure_delete: "您确定要删除这个数据权限吗？",
    devices_data: "一些设备和其他数据绑定到这个零售商，<br />无法删除。",
    required: "请选择数据权限",
    retail_client: "零售商权限",
    partner_permissions: "代理商权限",

    retail_client_info: "零售商信息",
    partner_info: "代理商信息",

  },


  datascope: {
    all_data: "顶级权限",
    custom_tip: "自定义代理商权限",
    custom_retail: "自定义零售商权限",
    currently_highest: "当前权限为最高权限，无法自定义",
    all_data_tip: "当前权限包含所有用户和门店，无法自定义",
    retail_client_name: "零售商名称",
    retail_client_account: "零售商账号",
    retail_client_name_placeholder: "请输入零售商名称",
    enter_outlet_name: "请输入门店名称",
    "principals_selection": "零售商选择",
    "partner_selection": "代理商选择",
    "principals_selection_placeholder": "请输入名称",
    "partner_selection_placeholder": "请输入名称",
    "principals_list": "零售商列表",
    "partner_list": "代理商列表",
    "selected_principals": "已选择零售商",
    "selected_partner_companies": "已选择代理商",
    selected: "已选中",
    outlet_selection: "门店选择",
    selected_outlets: "已选中门店"

  },
  area: {
    title: "区域",
    new_area: "新增区域",
    messageBox_title: "删除区域",
    area_area_name: "区域名称",
    area_del: '您确定要删除"{{name}}"区域吗？',
    ips_enter_region: "请选择一个区域",
    select_an_area: "选择一个区域",
    accelerate: "区域加速",
    region_center: "区域中心点位置",
    superior_area: "上级区域",
    enter_region_name: "输入区域名称",
    area_required: "请选择门店位置",
    "operation.create": "创建区域",
    "operation.update": "更新区域",
    "operation.delete": "删除区域",
  },
  roles: {
    title: "权限组",
    name: "角色名称",
    department_name: "部门名称",
    department_level: "部门分类",
    enter_roles_name: "请输入角色名称",
    name_required: "角色名称是必填项",
    add_auth_level: "新增权限组",
    edit_auth_level: "编辑权限组",
    view_auth_level: "预览权限组",
    authorization_level_placeholder: "请选择应用角色",
  },



  role: {
    "operation.update": "修改权限",
    "operation.create": "新增权限",
    "operation.delete": "删除权限",
  },
  product_info: {
    title: "商品信息",
    add_product_element: "新增商品信息",
    is_empty: "空模板",
    outlet: "门店"
  },
  outlet_info: {
    title: "门店信息",
    add_outlet_element: "新增门店信息",
  },
  data_dict: {},
  device: {
    title: "设备管理",
    device_name: "设备:",
    device_status: "状态:",
    device_title: "设备名称",
    adb_mode: "ADB模式",
    device_detail: "设备详情",
    device_protocolType: "设备协议类型",
    edit_title: "编辑设备",
    add_title: "向门店添加设备",
    sn: "设备 SN",
    model: "设备型号",
    online_status: "在线状态",
    device_number: "请输入设备序列号",
    main_device: "子设备",
    other_device: "其他设备",
    add: "添加",
    node_test: "注：主设备为相机，可以独立工作，无需使用算法盒。",
    bind_device: "将设备绑定到一个零售商或一个零售网点",
    bind_device_tips: "请指定设备到一个地点和区域",
    bind_device_site: "此设备将同步站点的相同时区",
    delete_tips_sure: "你确定要删除当前设备吗？",
    "operation.create": "新增设备",
    "operation.update": "更新设备",
    "operation.delete": "删除设备",
    "event.hook.operation.update": "更新操作事件",
    "channel.operation.update": "更新操作频道",
    "event.hook.operation.save": "保存操作事件",
    "channel.operation.save": "保存操作频道",
    "operation.reboot": "设备重启",
    "operation.shutdown": "设备关机",
    "operation.switch.device.time": "设备切换时间"

  },

  outlets: {
    name: "门店名称",
    address: "门店地址",
    email: "电子邮箱",
    phone: "手机",
    add_outlet: "新增门店",
    name_required: "请输入门店名称",
    outlet_location: "门店位置",
    main_title: "零售主要数据",
    outlet: "门店",
    please_select_outlet: "请选择门店",
    view_outlet: "查看门店",
    organization_name: "组织名称",
    region: "区域",
    organization_owner_email: "组织所有者邮箱",
    organization_owner_mobile: "组织所有者手机",
    enter_outlet_name: "请输入门店名称",
    outlet_name_required: "门店名称是必填项",
    contracts_mobile: "合同手机",
    enter_contracts_mobile: "请输入合同手机",
    mobile_number_required: "手机号码是必填项",
    invalid_mobile_format: "无效的手机号码格式",
    contracts_email: "合同电子邮箱地址",
    enter_contracts_email: "请输入合同电子邮箱地址",
    contracts_email_required: "合同电子邮箱地址是必填项",
    invalid_email_format: "无效的电子邮箱格式",
    outlet_location: "门店位置",
    select_an_area: "选择一个区域",
    address_required: "地址是必填项",
    time_zone: "时区",
    select_time_zone: "选择时区",
    time_zone_required: "时区是必填项",
    enter_address: "请输入地址",
    outlet_list: "门店列表",
    sure: "您确定要永久删除此门店吗?",
    edit_outlet_principal: "编辑零售商门店",
    add_outlet_principal: "新增零售商门店",
    please_enter_outletName: "请输入门店名称",
    outlets_delete: "此门店下有设备，无法删除。",

  },

  dict: {
    "data.operation.create": "创建字典数据",
    "data.operation.update": "更新字段数据",
    "data.operation.delete": "删除字典数据",
    "type.operation.create": "创建字典类型",
    "type.operation.update": "更新字典类型",
    "type.operation.delete": "删除字典类型",
  },

  product: {
    name: "商品名称",
    product: "商品",
    partner_company_name: "合作公司名称",
    size: "尺寸",
    bar_code: "条形码",
    category_level_1: "类别级别1",
    category_level_2: "类别级别2",
    list: "商品列表",
    add: "新增商品",
    edit: "编辑商品",
    please_enter: "请输入",
    sure_delete_product: "你确定要删除这个商品吗？",
    "type.operation.create": "新增产品类型",
    "type.operation.update": "更新产品类型",
    "type.operation.delete": "删除产品类型",
    "operation.update": "编辑商品",
    "operation.create": "新增商品",
    "operation.delete": "删除商品",
    isEmpty_required: "请选择是否为空模板",
    productName_required: "请输入商品名称",
  },

  storage: {
    "history.operation.delete": "删除上传文件",
  },

  event: {
    "matedata.operation.export": "导出元数据",
  },

  people: {
    "counting.operation.export": "基础客流导出",
  },

  principal: {
    principal_name: "零售商名称",
    title: "零售商",
    delete_content_on: "一些设备和其他数据绑定到这个零售商，<br />无法删除。",
    delete_content: "您确定要永久删除这个零售商吗？",
    add_principal: "新增零售商",
    edit_principal: "编辑零售商",
    principal_name_required: "请输入零售商名称",
    enter_principal_name: "请输入零售商名称",
    principal_owner_email: "零售商所有者邮箱",
    principal_owner_email_required: "请输入零售商所有者邮箱",
    total_outlets: "门店总数",
    enter_total_outlets: "请输入门店总数",
    mobile_number: "手机号码",
    mobile_number_required: "请输入手机号码",
    outlet_location: "门店位置",
    select_an_area: "选择一个区域",
    area_required: "请选择区域",
    view_principal: "查看零售商",
    organization_name: "组织名称",
    region: "区域",
    organization_owner_email: "组织所有者邮箱",
    organization_owner_mobile: "组织所有者手机",
    address: "地址",
    "operation.create": "新增零售商",
    "operation.update": "更新零售商",
    "operation.delete": "删除零售商",
  },
  principal_user: {
    title: "零售商员工",
    view: "预览",
    logo: "公司标志",
    firstName: "名",
    lastName: "姓",
    email: "电子邮箱地址",
    phone: "手机号码",
    password: "密码",
    confirmPassword: "确认密码",
    dataScopeId: "负责零售商",
    add_principal: "新增零售商用户",
    edit_principal: "编辑零售商用户",
    followAuthorizationLevel: "跟随授权级别",
    principal_required: "零售商是必填项",
    firstName_required: "名是必填项",
    lastName_required: "姓是必填项",
    email_required: "电子邮箱地址是必填项",
    mobile_required: "手机号码是必填项",
    password_required: "密码是必填项",
    confirmPassword_required: "确认密码是必填项",
    password_mismatch: "确认密码和密码不一致",
    sure_delete: "您确定要永久删除这个零售商吗？",
    delete_no_delete: "一些设备和其他数据绑定到这个零售商，<br />无法删除。",

    required_email: "电子邮箱地址是必填项",
    required_region: "区域是必填项",
    required_mobile: "手机号码是必填项",
    required_org_name: "组织名称是必填项",
  },

  partner: {
    title: "代理商",
    logo: "代理商 Logo",
    partner_name: "代理商名称",
    enter_partner_name: "请输入代理商名称",
    add_partner: "新增代理商",
    edit_partner: "编辑代理商",
    delete_content_on: "一些设备和其他数据绑定到这个零售商，<br />无法删除。",
    delete_content: "您确定要永久删除这个零售商吗？",
    email: "电子邮箱",
    email_required: "电子邮箱是必填项",
    email_format_error: "邮箱格式有误",
    mobile_number: "手机号码",
    mobile_number_required: "手机号码是必填项",
    mobile_format_error: "号码格式有误",
    data_permission: "数据权限",
    follow_authorization_level: "跟随授权级别",
    data_permission_required: "数据权限是必填项",
    partner_location: "代理商位置",
    enter_partner_location: "输入代理商位置",
    location_required: "代理商位置是必填项",
    password: "密码",
    password_required: "密码是必填项",
    password_format_error: "密码必须包含大小写字母、数字和特殊符号，且长度在12-64之间",
    confirm_password: "确认密码",
    confirm_password_required: "确认密码是必填项",
    password_mismatch: "确认密码和密码不一致",
    view: "预览代理商",

    "operation.create": "新增代理商",

    "operation.delete": "删除代理商",
    "operation.update": "编辑代理商"
  },


  tenant: {
    "operation.update": "修改租户",
    "operation.create": "新增租户",
    "operation.delete": "删除租户",
  },

  tenantEmployee: {
    "operation.update": "修改租户用户",
    "operation.create": "新增租户用户",
    "operation.delete": "删除租户用户",
  },


  partner_user: {
    title: "代理商用户",
    logo: "公司标志",
    firstName: "名",
    lastName: "姓",
    email: "电子邮箱地址",
    phone: "手机号码",
    password: "密码",
    confirmPassword: "确认密码",
    dataScopeId: "负责代理商",
    followAuthorizationLevel: "跟随授权级别",
    principal_required: "代理商是必填项",
    firstName_required: "名是必填项",
    lastName_required: "姓是必填项",
    email_required: "电子邮箱地址是必填项",
    mobile_required: "手机号码是必填项",
    password_required: "密码是必填项",
    confirmPassword_required: "确认密码是必填项",
    password_mismatch: "确认密码和密码不一致",
    sure_delete: "您确定要永久删除这个代理商吗？",
    delete_no_delete: "一些设备和其他数据绑定到这个代理商，<br />无法删除。",
    view_title: "查看代理商员工",
    first_name: "名",
    last_name: "姓",
    region: "区域",
    owner_email: "组织所有者邮箱",
    owner_mobile: "组织所有者手机",
    area_name: "区域名称",
    address: "地址",
  },

  login_log: {
    title: "登录日志",
    account: "登录账号",
    user_name: "登录用户名",
    login_time: "访问时间",
    login_ip: "登录IP",
    login_log: "登录日志",
    login_location: "登录地址",
    login_message: "登录信息",
    status: "状态",
  },

  operation_log: {
    name: "操作日志",
    title: "操作方法",
    account: "操作账号",
    ip: "操作IP",
    address: "操作地址",
    time: "操作时间",
  },

  operation: {
    playlist_create: "创建播放清单",
    "upload.file": "上传文件",
    "material.group.create": "创建素材分组",
    "material.group.delete": "删除素材分组",
    "material.group.update": "修改素材分组",
    "playlist.create": "创建播放清单",
    "playlist.delete": "删除播放清单",
    "playlist.update": "修改播放清单",
    "total.outlet.create": "创建门店总数",
    "total.outlet.delete": "删除门店总数",
    "total.outlet.update": "修改门店总数",
    "material.del": "删除素材",
  },


  template: {
    "operation.update": "更新消息模板",
    "operation.save": "保存消息模板",
    "operation.delete": "删除消息模板",
  },
  dict: {
    "data.operation.create": "创建字典数据",
    "data.operation.update": "更新字典数据",
    "data.operation.delete": "删除字典数据",
    "type.operation.create": "创建字典类型",
    "type.operation.update": "更新字典类型",
    "type.operation.delete": "删除字典类型",
  },

  outlet: {
    "type.operation.create": "创建门店类型",
    "type.operation.update": "更新门店类型",
    "type.operation.delete": "删除门店类型",
    "operation.create": "创建门店",
    "operation.update": "修改门店",
    "operation.delete": "删除门店"

  },

  principalEmployee: {
    "operation.update": "更新零售商用户",
    "operation.delete": "删除零售商用户",
    "operation.create": "新增零售商用户",
  },


  storage: {
    "history.operation.delete": "删除操作历史",
  },

  people: {
    "counting.operation.export": "导出基础人流数据",
  },

  event: {
    "matedata.operation.export": "导出元数据",
  },

  layout: {
    "template.operation.update": "更新布局模板",
    "template.operation.save": "保存布局模板",
    "template.operation.delete": "删除布局模板",
    "template.operation.create": "创建布局模板",

  },

  material: {
    "report.operation.export": "导出素材"
  },


  dataScope: {
    "operation.update": "修改数据权限",
    "operation.create": "新增数据权限",
    "operation.delete": "删除数据权限",
  },

  subscription: {
    add_subscription: "新增订阅",
    update_subscription: "升级套餐",
    per_device_month_annually: "每设备/月（按年计费）<br /> 月付US$11",
    add_device: "增加设备",
    extend_subscription: "延长订阅",
    subscription_record: "订阅记录",
    unknown_type: "未知订阅",
    trial_package: "试用版",
    basic_package: "基础套餐",
    advanced_package: "高级版",
    Update: "更新",
    extend: "延长",
    more: "更多设备",
    subscription: "订阅",
    cmsText: "Screen Direct",
    nutagText: "NuTag",
    zataText: "ZATA",
    starter_package: "入门版",
    professional_package: "高级版",
    summit_package: "专业版",
    includes: "包含",
    free: "免费",
    freeTip: "15天内2台设备免费",
    getStarted: "开始使用",
    comingSoon: "即将推出",
    customerName: "客户名称",
    enterCustomerName: "输入客户名称",
    customerAccount: "客户账户",
    accountCreatedBy: "账户创建者",
    contractID: "合同ID",
    enterContractID: "输入合同ID",
    contractAmount: "合同金额",
    enterContractAmount: "输入合同金额",
    subscriptionPackage: "订阅套餐",
    subscriptionCreationDate: "订阅创建日期",
    expirationDate: "到期日期",
    activeDate: "激活日期",
    selectExpirationDate: "选择到期日期",
    selectActiveDate: "选择激活日期",
    numberOfDevice: "设备数量",
    enterNumberOfDevice: "输入设备数量",
    accountInformation: "账户信息",
    customerEmailAddress: "客户电子邮箱地址",
    enterCustomerEmailAddress: "输入客户电子邮箱地址",
    selectCustomer: "选择客户",
    area: "区域",
    enterArea: "请输入区域名称",
    mobileNumber: "手机号码",
    enterMobileNumber: "输入手机号码",
    contractInformation: "合同信息",
    amountUnit: "金额单位",
    selectAmountUnit: "选择金额单位",
    contractFile: "合同文件",
    uploadContract: "上传合同",
    uploadCompanyLogo: "上传公司标志",
    submit: "提交",
    emailAddress: "电子邮箱地址",
    clickSelectCustomer: "点击输入框选择客户",
    SelectCustomer: "选择客户",
    createCustomer: "创建客户",
    modifyTitle: "修改订阅",
    positive: "请输入正整数",
    tipMsg: "设备数量超过套餐限制，请先删除设备并刷新页面",
    tipMsg2: "设备数量超过套餐限制，请联系管理员",
    tipMsg3: "您没有菜单权限，请联系管理员",
    payment: "支付",
    paymentMsg: "您确定要支付当前订单吗？",
    singleMaterial: "单个内容",
    US$4: "4美元",
    US$10: "10美元",
    basic_package_tip: "每设备/月（年付）11美元月付",
    areaNameTip: "必须以字母或汉字开头，只能包含字母、数字、汉字、空格和下划线",
    payStatus: "支付状态",
    paid: "已支付",
    unpaid: "未支付",
    lengthTip: "长度不能超过{{length}}。",
    minLengthTip: "长度必须大于{{length}}。",
    moreThen: "金额不能为负数。",
    numberMoreThen: "金额不能超过{{number}}。",
    singleFile: "只允许上传单个文件。",
    dropTip: "拖放到此处上传。",
    limtFileSize: "文件大小不能超过{{tip}}。",
    Account_Information: "账户信息",
    contract_information: "合同信息",
    enter_Dev_number: "请输入设备数量",
    clinet_name: "客户端名称",
    please_enter_clinet_name: "请输入客户端名称",
    customer_email_address: "客户电子邮箱地址",
    please_enter_email_address: "请输入客户电子邮箱地址",
    email_formatted: "电子邮箱格式不正确",
    enter_customer_code: "请输入客户代码",
    client_Code: "客户代码",
    please_select_location: "请选择位置。",
    please_enter_first_name: "请输入名字",
    please_enter_last_name: "请输入姓氏",
    please_select_country: "请选择国家",
    please_select_province: "请选择省份",
    please_select_city: "请选择城市",
    please_formatted_phone: "请输入正确的手机号码",
    only_number: "只能输入数字",
    Monthly: "每月",
    Annually: "每年",
    extend_months: "延长订阅月数",
    extend_years: "延长订阅年数",
    subMonths: "订阅月数",
    subAnnually: "订阅年数",
    please_enter_number_months: "请输入月数",
    please_enter_number_Annually: "请输入年数",
    please_subscription_number: "请输入订阅的月数/年数",
    please_select_zoonTime: "请选择时区",
    please_select_startTime: "请选择开始时间",
    please_select_endTime: "请选择结束时间",
    Contract_ID: "合同ID",
    please_enter_contract_Id: "请输入合同ID",
    please_enter_contract_amount: "请输入合同金额",
    please_enter_contract_unit: "请选择合同单位",
    Contract_Amount: "合同金额",
    Contract_Unit: "合同单位",
    Device_Count: "设备数量",
    subCreatedAt: "订阅创建时间",
    select_retail_clinet: "请选择一个客户",
    includes: "包含 :",
    get_started: "开始使用",
    numberOfDevice: "设备数量",
    expirationDate: "到期日期",
    activeDate: "激活日期",
    selectExpirationDate: "选择到期日期",
    selectActiveDate: "选择激活日期",
    subsctiption_content: "订阅",
    email_address: "邮箱地址",
    enter_email_address: "请输入客户电子邮箱",
    email_required: "客户电子邮箱是必填项",
    email_format: "邮箱格式有误",
    account_information: "账户信息",
    contract_information: "合同信息",
    mobile_number: "手机号码",
    create_principal: "新增零售商",
    select_principal: "选择零售商",
    principal_region: "零售商区域",
    enter_region_name: "请输入区域名称",
    "operation.create": "新增订阅",
    "operation.update": "升级套餐",
  },

  user: {
    "operation.delete": "删除用户",
    "operation.update": "修改用户",
    "operation.create": "新增用户",
  },

  password: {
    "operation.verify": "验证原始密码",
    "operation.update": "修改密码",
    security_account: "  定期更改密码以保护您的账户安全",
    change_password: "修改密码"
  },


  login: {
    email: "邮箱",
    mobile: "手机",
    password: "密码",
    remember_me: "记住密码",
    forgot_password: "忘记密码？",
    login_button: "登录",
    verification_code: "验证码",
    enter_email: "请输入邮箱",
    enter_password: "请输入密码",
    enter_verification_code: "请输入验证码",
    user_agreement: "我已阅读并同意",
    user_agreement_link: "用户协议",
    privacy_policy_link: "隐私政策",
    and: "和"
  },
};
export default pages;
